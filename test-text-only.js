const axios = require('axios');

async function testTextOnly() {
  try {
    console.log('🚀 Testing text-only query...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('✅ Response received');
    console.log('Status:', response.status);
    console.log('Data:', response.data);
    
  } catch (error) {
    console.error('❌ Error:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

testTextOnly();
