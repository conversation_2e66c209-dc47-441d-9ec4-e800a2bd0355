const { createWorker } = require('tesseract.js');
const fs = require('fs');

async function extractAndShowText() {
  try {
    const imagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    if (!fs.existsSync(imagePath)) {
      console.log('❌ Image not found at:', imagePath);
      return;
    }
    
    console.log('✅ Image found, extracting text...');
    console.log('📊 Image size:', fs.statSync(imagePath).size, 'bytes');
    
    // Extract text using Tesseract directly
    const worker = await createWorker();
    await worker.loadLanguage('eng');
    await worker.initialize('eng');
    
    console.log('🔍 Running OCR...');
    const { data: { text } } = await worker.recognize(imagePath);
    await worker.terminate();
    
    console.log('\n📝 EXTRACTED TEXT FROM IMAGE:');
    console.log('=' .repeat(80));
    console.log(text.trim());
    console.log('=' .repeat(80));
    console.log(`\n📊 Total characters extracted: ${text.trim().length}`);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

console.log('🚀 Extracting text from image...');
extractAndShowText();
