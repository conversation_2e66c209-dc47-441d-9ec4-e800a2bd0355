const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Test non-streaming (existing functionality)
async function testNonStreaming() {
  try {
    console.log('🚀 Testing NON-STREAMING (existing functionality)...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is the derivative of x^3 + 2x^2 + x + 1?',
      stream: false // Explicitly disable streaming
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });
    
    console.log('✅ Non-streaming response received');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE (Non-streaming):');
      console.log('-'.repeat(50));
      console.log(response.data.response.substring(0, 300) + '...');
      console.log('-'.repeat(50));
      
      console.log('\n📊 Response metadata:');
      console.log('- Success:', response.data.success);
      console.log('- Response length:', response.data.response.length);
      console.log('- Query:', response.data.query);
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing non-streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test streaming with the same endpoint
async function testStreaming() {
  try {
    console.log('\n🚀 Testing STREAMING (new functionality)...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'Explain the concept of derivatives in calculus with examples.',
      stream: true // Enable streaming
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'stream',
      timeout: 120000
    });
    
    console.log('✅ Streaming connection established');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    let startTime = Date.now();
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log('\n\n✅ Streaming completed!');
            console.log('📊 Statistics:');
            console.log(`   - Total content: ${fullContent.length} characters`);
            console.log(`   - Duration: ${duration} seconds`);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received:', {
                query: parsed.query.substring(0, 50) + '...',
                hasFile: parsed.hasFile,
                streaming: parsed.streaming
              });
              console.log('\n🤖 AI Response (streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test streaming with image
async function testImageStreaming() {
  try {
    console.log('\n🚀 Testing IMAGE STREAMING...');
    
    const imagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    if (!fs.existsSync(imagePath)) {
      console.log('❌ Image not found, skipping image streaming test');
      return;
    }
    
    console.log('✅ Image found, starting streaming test');
    
    const form = new FormData();
    form.append('file', fs.createReadStream(imagePath));
    form.append('query', 'Please solve the first physics problem in this image step by step.');
    form.append('stream', 'true'); // Enable streaming
    
    const response = await axios.post('http://localhost:3001/api/chat', form, {
      headers: {
        ...form.getHeaders(),
      },
      responseType: 'stream',
      timeout: 300000 // 5 minutes for complex problems
    });
    
    console.log('✅ Image streaming connection established');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    let startTime = Date.now();
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log('\n\n✅ Image streaming completed!');
            console.log('📊 Statistics:');
            console.log(`   - Total content: ${fullContent.length} characters`);
            console.log(`   - Duration: ${duration} seconds`);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received:', {
                query: parsed.query.substring(0, 50) + '...',
                hasFile: parsed.hasFile,
                extractedTextLength: parsed.extractedText?.length || 0,
                streaming: parsed.streaming
              });
              console.log('\n🤖 AI Response (streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Image stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing image streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run all tests
async function runAllTests() {
  console.log('🧪 Testing unified /api/chat endpoint with streaming support...');
  console.log('🔗 Backend URL: http://localhost:3001/api/chat');
  console.log('📋 Testing both streaming and non-streaming modes');
  console.log('');
  
  await testNonStreaming();
  
  // Wait between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testStreaming();
  
  // Wait between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testImageStreaming();
  
  console.log('\n🎉 All unified endpoint tests completed!');
}

runAllTests();
