const admin = require('firebase-admin');
const path = require('path');
const fs = require('fs');

console.log('🔥 Testing Firebase Admin SDK initialization...');

try {
  const serviceAccountPath = path.join(__dirname, 'firebase-service-account.json');
  console.log('📁 Service account path:', serviceAccountPath);
  
  if (fs.existsSync(serviceAccountPath)) {
    console.log('✅ Service account file exists');
    
    const serviceAccount = require('./firebase-service-account.json');
    console.log('✅ Service account file loaded');
    console.log('📋 Project ID:', serviceAccount.project_id);
    console.log('📧 Client Email:', serviceAccount.client_email);
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: serviceAccount.project_id
    });
    
    console.log('✅ Firebase Admin SDK initialized successfully');
    console.log('📊 Apps initialized:', admin.apps.length);
    
    // Test token verification with a dummy token
    console.log('\n🧪 Testing token verification with invalid token...');
    try {
      await admin.auth().verifyIdToken('invalid-token');
      console.log('❌ Should have failed');
    } catch (error) {
      console.log('✅ Invalid token correctly rejected:', error.code);
    }
    
  } else {
    console.log('❌ Service account file not found');
  }
  
} catch (error) {
  console.error('❌ Firebase initialization failed:', error.message);
  console.error('Stack trace:', error.stack);
}

console.log('\n🎯 Firebase test completed');
