const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testSimpleQuery() {
  try {
    const sourceImagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    if (!fs.existsSync(sourceImagePath)) {
      console.log('❌ Source image not found');
      return;
    }
    
    console.log('✅ Image file found');
    
    // Create form data with a very simple query
    const form = new FormData();
    form.append('file', fs.createReadStream(sourceImagePath));
    form.append('query', 'What physics problems do you see in this image? Just describe them briefly.');
    
    console.log('📤 Sending simple query to backend...');
    
    const response = await axios.post('http://localhost:3001/api/chat', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 60000, // 1 minute timeout
    });
    
    console.log('✅ Response received');
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE:');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
      if (response.data.extractedText) {
        console.log('\n📝 EXTRACTED TEXT:');
        console.log('-'.repeat(30));
        console.log(response.data.extractedText);
        console.log('-'.repeat(30));
      }
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else if (error.request) {
      console.error('Network error');
    } else {
      console.error('Error:', error.message);
    }
  }
}

console.log('🚀 Testing with simple query...');
testSimpleQuery();
