<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JUBuddyAI Streaming Demo</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .input-section {
            margin-bottom: 30px;
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #34495e;
        }
        input[type="text"], textarea, input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-stream {
            background-color: #3498db;
            color: white;
        }
        .btn-stream:hover {
            background-color: #2980b9;
        }
        .btn-normal {
            background-color: #95a5a6;
            color: white;
        }
        .btn-normal:hover {
            background-color: #7f8c8d;
        }
        .btn-clear {
            background-color: #e74c3c;
            color: white;
        }
        .btn-clear:hover {
            background-color: #c0392b;
        }
        .response-section {
            margin-top: 30px;
        }
        .response-box {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            min-height: 200px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .metadata {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 12px;
            color: #0066cc;
        }
        .streaming-indicator {
            display: none;
            color: #28a745;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .streaming-indicator.active {
            display: block;
        }
        .typing-indicator {
            display: none;
            color: #6c757d;
            font-style: italic;
        }
        .typing-indicator.active {
            display: inline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 JUBuddyAI Streaming Demo</h1>
        
        <div class="input-section">
            <div class="input-group">
                <label for="query">Your Question:</label>
                <textarea id="query" placeholder="Ask me anything about math, physics, chemistry, or upload an image with problems to solve...">Explain quantum mechanics in simple terms with examples.</textarea>
            </div>
            
            <div class="input-group">
                <label for="file">Upload Image (Optional):</label>
                <input type="file" id="file" accept=".jpg,.jpeg,.png,.pdf,.docx,.txt">
            </div>
            
            <div class="button-group">
                <button class="btn-stream" onclick="testStreaming()">🌊 Test Streaming</button>
                <button class="btn-normal" onclick="testNormal()">📄 Test Normal</button>
                <button class="btn-clear" onclick="clearResponse()">🗑️ Clear</button>
            </div>
        </div>
        
        <div class="response-section">
            <div id="status"></div>
            <div id="metadata"></div>
            <div class="streaming-indicator" id="streamingIndicator">
                🔄 Streaming response... <span class="typing-indicator" id="typingIndicator">●</span>
            </div>
            <div class="response-box" id="responseBox">Ready to test streaming! Try asking a question or uploading an image.</div>
        </div>
    </div>

    <script>
        function setStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
        }

        function setMetadata(data) {
            const metadataDiv = document.getElementById('metadata');
            if (data) {
                metadataDiv.className = 'metadata';
                metadataDiv.innerHTML = `
                    <strong>Metadata:</strong><br>
                    Query: ${data.query?.substring(0, 100)}${data.query?.length > 100 ? '...' : ''}<br>
                    Has File: ${data.hasFile ? 'Yes' : 'No'}<br>
                    ${data.extractedTextLength ? `Extracted Text: ${data.extractedTextLength} characters<br>` : ''}
                    Streaming: ${data.streaming ? 'Yes' : 'No'}
                `;
            } else {
                metadataDiv.className = '';
                metadataDiv.innerHTML = '';
            }
        }

        function clearResponse() {
            document.getElementById('responseBox').textContent = 'Ready to test streaming! Try asking a question or uploading an image.';
            document.getElementById('status').textContent = '';
            document.getElementById('metadata').innerHTML = '';
            document.getElementById('streamingIndicator').classList.remove('active');
            document.getElementById('typingIndicator').classList.remove('active');
        }

        async function testStreaming() {
            const query = document.getElementById('query').value.trim();
            const fileInput = document.getElementById('file');
            const responseBox = document.getElementById('responseBox');
            
            if (!query) {
                setStatus('Please enter a question', 'error');
                return;
            }

            setStatus('Starting streaming request...', 'info');
            responseBox.textContent = '';
            document.getElementById('streamingIndicator').classList.add('active');
            document.getElementById('typingIndicator').classList.add('active');

            try {
                const formData = new FormData();
                formData.append('query', query);
                
                if (fileInput.files[0]) {
                    formData.append('file', fileInput.files[0]);
                }

                const response = await fetch('http://localhost:3001/api/chat/stream', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullContent = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            
                            if (data === '[DONE]') {
                                document.getElementById('streamingIndicator').classList.remove('active');
                                document.getElementById('typingIndicator').classList.remove('active');
                                setStatus(`Streaming completed! Total: ${fullContent.length} characters`, 'success');
                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                
                                if (parsed.type === 'metadata') {
                                    setMetadata(parsed);
                                    setStatus('Receiving streaming response...', 'info');
                                } else if (parsed.content) {
                                    fullContent += parsed.content;
                                    responseBox.textContent = fullContent;
                                    responseBox.scrollTop = responseBox.scrollHeight;
                                } else if (parsed.error) {
                                    setStatus(`Error: ${parsed.error}`, 'error');
                                    document.getElementById('streamingIndicator').classList.remove('active');
                                    return;
                                }
                            } catch (parseError) {
                                // Skip invalid JSON
                            }
                        }
                    }
                }
            } catch (error) {
                setStatus(`Streaming error: ${error.message}`, 'error');
                document.getElementById('streamingIndicator').classList.remove('active');
                document.getElementById('typingIndicator').classList.remove('active');
            }
        }

        async function testNormal() {
            const query = document.getElementById('query').value.trim();
            const fileInput = document.getElementById('file');
            const responseBox = document.getElementById('responseBox');
            
            if (!query) {
                setStatus('Please enter a question', 'error');
                return;
            }

            setStatus('Sending normal request...', 'info');
            responseBox.textContent = 'Processing...';

            try {
                const formData = new FormData();
                formData.append('query', query);
                
                if (fileInput.files[0]) {
                    formData.append('file', fileInput.files[0]);
                }

                const response = await fetch('http://localhost:3001/api/chat', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    setMetadata({
                        query: data.query,
                        hasFile: !!data.extractedText,
                        extractedTextLength: data.extractedText?.length,
                        streaming: false
                    });
                    responseBox.textContent = data.response;
                    setStatus(`Normal request completed! Response: ${data.response.length} characters`, 'success');
                } else {
                    responseBox.textContent = `Error: ${data.error}`;
                    setStatus(`Request failed: ${data.error}`, 'error');
                }
            } catch (error) {
                responseBox.textContent = `Error: ${error.message}`;
                setStatus(`Request error: ${error.message}`, 'error');
            }
        }

        // Animate typing indicator
        setInterval(() => {
            const indicator = document.getElementById('typingIndicator');
            if (indicator.classList.contains('active')) {
                const current = indicator.textContent;
                indicator.textContent = current === '●' ? '●●' : current === '●●' ? '●●●' : '●';
            }
        }, 500);
    </script>
</body>
</html>
