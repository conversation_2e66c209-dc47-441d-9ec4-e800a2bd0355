const axios = require('axios');
require('dotenv').config();

async function testDirectAPI() {
  try {
    console.log('🚀 Testing direct DeepSeek API call...');
    
    const extractedText = `3. An object of height 1.5 cm is placed at a distance 40 cm in front of a convex lens of focal length 20 cm. Behind the convex lens a concave lens of focal length -10 cm is placed at a distance of 15 cm from the convex lens. Determine the system matrix. Find the size, position and nature of the image.

4. A glass lens with radii r1 = +3.0 cm and r2 = + 3.0 cm has an refractive index of 1.60 and a thickness of 3 cm. It is placed in the end of tank so that air is contact with face r1, and a transparent oil of refractive index 1.30 is in contact with face r2. Find the primary and secondary focal lengths, the power of the system of lens, calculate the positions of focal points, principal points and nodal points.`;

    const response = await axios.post('https://api.deepseek.com/chat/completions', {
      model: 'deepseek-r1:free',
      messages: [
        {
          role: 'user',
          content: `Please solve these physics problems step by step: ${extractedText}`
        }
      ],
      temperature: 0.7,
      max_tokens: 4000
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json'
      },
      timeout: 120000 // 2 minutes
    });
    
    console.log('✅ Response received from DeepSeek API');
    console.log('Status:', response.status);
    
    const messageObj = response.data.choices[0].message;
    console.log('\n📊 Response structure:');
    console.log('- Content length:', messageObj.content?.length || 0);
    console.log('- Reasoning length:', messageObj.reasoning?.length || 0);
    
    let finalContent = messageObj.content;
    if ((!finalContent || finalContent.trim() === '') && messageObj.reasoning) {
      console.log('🔍 Using reasoning field as content is empty');
      finalContent = messageObj.reasoning;
    }
    
    console.log('\n🤖 AI RESPONSE:');
    console.log('=' .repeat(80));
    console.log(finalContent);
    console.log('=' .repeat(80));
    
  } catch (error) {
    console.error('❌ Error calling DeepSeek API:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

testDirectAPI();
