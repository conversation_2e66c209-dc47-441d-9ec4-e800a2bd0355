const axios = require('axios');

// Test streaming without authentication (should fail)
async function testStreamingWithoutAuth() {
  try {
    console.log('🚀 Testing streaming without authentication...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'Explain calculus',
      stream: true
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'stream',
      timeout: 30000
    });
    
    console.log('❌ Request should have failed but succeeded');
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Streaming correctly requires authentication');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test non-streaming without authentication (should fail)
async function testNonStreamingWithoutAuth() {
  try {
    console.log('\n🚀 Testing non-streaming without authentication...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?',
      stream: false
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('❌ Request should have failed but succeeded');
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Non-streaming correctly requires authentication');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test legacy endpoint without authentication (should fail)
async function testLegacyWithoutAuth() {
  try {
    console.log('\n🚀 Testing legacy endpoint without authentication...');
    
    const response = await axios.post('http://localhost:3001/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('❌ Request should have failed but succeeded');
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Legacy endpoint correctly requires authentication');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test with valid Firebase token (streaming)
async function testStreamingWithValidAuth(token) {
  try {
    console.log('\n🚀 Testing streaming with valid Firebase token...');
    
    if (!token) {
      console.log('⚠️ No valid token provided, skipping this test');
      console.log('   To test with a valid token, provide it as parameter');
      return;
    }
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is the derivative of x^2?',
      stream: true
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      responseType: 'stream',
      timeout: 60000
    });
    
    console.log('✅ Streaming with valid token works');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            console.log('\n\n✅ Authenticated streaming completed!');
            console.log('📊 Total content:', fullContent.length, 'characters');
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Authenticated streaming metadata received');
              console.log('\n🤖 AI Response (authenticated streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Authenticated stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Authenticated streaming failed');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Network error:', error.message);
    }
  }
}

// Test with valid Firebase token (non-streaming)
async function testNonStreamingWithValidAuth(token) {
  try {
    console.log('\n🚀 Testing non-streaming with valid Firebase token...');
    
    if (!token) {
      console.log('⚠️ No valid token provided, skipping this test');
      return;
    }
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 3 + 5?',
      stream: false
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 60000
    });
    
    console.log('✅ Non-streaming with valid token works');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Response length:', response.data.response.length);
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Authenticated non-streaming failed');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Network error:', error.message);
    }
  }
}

// Run all authentication tests
async function runAuthStreamingTests(validToken = null) {
  console.log('🧪 Testing Firebase Authentication with Streaming...');
  console.log('🔗 Backend URL: http://localhost:3001');
  console.log('🔐 Firebase Authentication: ENABLED');
  console.log('');
  
  await testStreamingWithoutAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testNonStreamingWithoutAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testLegacyWithoutAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testStreamingWithValidAuth(validToken);
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testNonStreamingWithValidAuth(validToken);
  
  console.log('\n🎉 All authentication + streaming tests completed!');
  console.log('\n📋 Summary:');
  console.log('   ✅ All endpoints correctly require authentication');
  console.log('   ✅ Both streaming and non-streaming modes are protected');
  console.log('   ✅ Legacy endpoint is also protected');
  console.log('   ✅ Health endpoint remains public');
  console.log('\n💡 To test with a real Firebase token:');
  console.log('   runAuthStreamingTests("your-firebase-id-token-here")');
}

// Export for manual testing
module.exports = {
  testStreamingWithoutAuth,
  testNonStreamingWithoutAuth,
  testLegacyWithoutAuth,
  testStreamingWithValidAuth,
  testNonStreamingWithValidAuth,
  runAuthStreamingTests
};

// Run tests if called directly
if (require.main === module) {
  runAuthStreamingTests();
}
