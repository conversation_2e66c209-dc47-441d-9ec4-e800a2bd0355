const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testPhysicsImage() {
  try {
    console.log('🧪 Testing physics problem with image...');
    
    // Check if image exists
    const imagePath = 'C:\\Users\\<USER>\\Downloads\\photo_2025-07-21_14-45-46.jpg';
    if (!fs.existsSync(imagePath)) {
      console.error('❌ Image file not found:', imagePath);
      return;
    }
    
    console.log('✅ Image file found');
    
    // Create form data
    const form = new FormData();
    form.append('file', fs.createReadStream(imagePath));
    form.append('query', 'Please solve all the physics problems shown in this image. Provide detailed step-by-step solutions for each problem, showing all calculations and explaining the physics concepts involved.');
    
    console.log('📤 Sending request to backend...');
    
    // Make request
    const response = await axios.post('http://localhost:3001/api/chat', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 300000, // 5 minutes timeout
    });
    
    console.log('✅ Response received!');
    console.log('📊 Status:', response.status);
    console.log('📝 Response length:', response.data.response?.length || 0, 'characters');
    
    if (response.data.success) {
      console.log('\n🎉 SUCCESS! Here\'s the solution:');
      console.log('=' .repeat(80));
      console.log(response.data.response);
      console.log('=' .repeat(80));
    } else {
      console.log('❌ Request failed:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('📝 Error data:', error.response.data);
    }
  }
}

// Run the test
testPhysicsImage();
