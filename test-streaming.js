const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

// Test streaming with text-only query
async function testTextStreaming() {
  try {
    console.log('🚀 Testing text-only streaming...');
    
    const response = await axios.post('http://localhost:3001/api/chat/stream', {
      query: 'Explain the concept of quantum entanglement in simple terms with examples.'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'stream',
      timeout: 120000
    });
    
    console.log('✅ Streaming connection established');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            console.log('\n\n✅ Streaming completed!');
            console.log('📊 Total content length:', fullContent.length);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received:', {
                query: parsed.query.substring(0, 50) + '...',
                hasFile: parsed.hasFile,
                streaming: parsed.streaming
              });
              console.log('\n🤖 AI Response (streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing text streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test streaming with image
async function testImageStreaming() {
  try {
    console.log('\n🚀 Testing image streaming...');
    
    const imagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    if (!fs.existsSync(imagePath)) {
      console.log('❌ Image not found, skipping image streaming test');
      return;
    }
    
    console.log('✅ Image found, starting streaming test');
    
    const form = new FormData();
    form.append('file', fs.createReadStream(imagePath));
    form.append('query', 'Please solve the physics problems in this image step by step.');
    
    const response = await axios.post('http://localhost:3001/api/chat/stream', form, {
      headers: {
        ...form.getHeaders(),
      },
      responseType: 'stream',
      timeout: 300000 // 5 minutes for complex problems
    });
    
    console.log('✅ Image streaming connection established');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            console.log('\n\n✅ Image streaming completed!');
            console.log('📊 Total content length:', fullContent.length);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received:', {
                query: parsed.query.substring(0, 50) + '...',
                hasFile: parsed.hasFile,
                extractedTextLength: parsed.extractedText?.length || 0,
                streaming: parsed.streaming
              });
              console.log('\n🤖 AI Response (streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Image stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing image streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run tests
async function runTests() {
  console.log('🧪 Starting streaming tests...');
  console.log('🔗 Backend URL: http://localhost:3001/api/chat/stream');
  console.log('');
  
  await testTextStreaming();
  
  // Wait a bit between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testImageStreaming();
  
  console.log('\n🎉 All streaming tests completed!');
}

runTests();
