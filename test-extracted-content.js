const axios = require('axios');

async function testExtractedContent() {
  try {
    console.log('🚀 Testing with extracted text content...');
    
    // This is the exact text that was extracted from the image
    const extractedText = `JE Sg ere corer So

3. An object of height 1.5 cm is placed at a distance 40 cm in front of a convex lens of
focal length 20 cm. Behind the convex lens a concave lens of focal length -10 cm is
, placed at a distance of 15 cm from the convex lens. Determine the system matrix
Find the size, position and nature of the image.

4. A glass lens with radii ry = +3.0 cm and rz = + 3.0 cm has an refractive index of
1.60 and a thickness of an 3 cm. It is placed in the end of tank so that air is contact
with face ry, and a transparent oil of refractive index 1.30 1s in contact with face ra.
Find

the primary and secondary focal lengths

the power of the system of lens.

calculate the positions of focal points, principal points and nodal points.
Lda 4 jamie' fa SAUL et esl Ben`;

    const response = await axios.post('http://localhost:3001/api/chat', {
      query: `Please solve the physics problems shown in this text: ${extractedText}`
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 120000 // 2 minutes
    });
    
    console.log('✅ Response received');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE:');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

testExtractedContent();
