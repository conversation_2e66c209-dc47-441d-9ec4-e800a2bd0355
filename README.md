# JUBuddyAI Backend

Node.js + Express backend for JUBuddyAI1 Android app with OCR and LLM integration.

## Features

- **Text Input**: Direct LLM interaction
- **Image Processing**: OCR extraction from JPG/PNG using Tesseract.js
- **PDF Processing**: Text extraction using pdf-parse
- **DOCX Processing**: Text extraction using mammoth
- **LLM Integration**: OpenRouter API with DeepSeek R1

## Installation

```bash
npm install
```

## Environment Variables

Create a `.env` file with:

```env
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
AI_MODEL_NAME=deepseek/deepseek-r1:free
PORT=3001
```

## API Endpoints

### Health Check
```http
GET /health
```

### Chat Endpoint
```http
POST /chat
Content-Type: multipart/form-data

Body:
- query (required): string - User's question
- file (optional): file - Image (.jpg/.png), PDF (.pdf), or Word (.docx)
```

## Usage Examples

### 1. Text Only Request
```bash
curl -X POST http://localhost:3001/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "What is artificial intelligence?"}'
```

### 2. Image Upload with OCR
```bash
curl -X POST http://localhost:3001/chat \
  -F "query=What text is in this image?" \
  -F "file=@/path/to/image.jpg"
```

### 3. PDF Upload
```bash
curl -X POST http://localhost:3001/chat \
  -F "query=Summarize this document" \
  -F "file=@/path/to/document.pdf"
```

### 4. DOCX Upload
```bash
curl -X POST http://localhost:3001/chat \
  -F "query=What are the key points?" \
  -F "file=@/path/to/document.docx"
```

## Response Format

```json
{
  "success": true,
  "query": "User's question",
  "extractedText": "Text extracted from file (if provided)",
  "response": "LLM response"
}
```

## Error Handling

The API provides comprehensive error handling for:
- Invalid file types
- File size limits (10MB max)
- OCR failures
- LLM API errors
- Missing required fields

## Deployment

### Render Deployment

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Set environment variables in Render dashboard
4. Build command: `npm install`
5. Start command: `node index.js`

### Environment Variables for Render

Add these to your Render environment variables:
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `PORT`: 10000 (Render's default)

## File Structure

```
├── index.js          # Main server file
├── package.json      # Dependencies and scripts
├── .env             # Environment variables
├── uploads/         # Temporary file uploads (auto-created)
└── README.md        # This file
```

## Supported File Types

- **Images**: .jpg, .jpeg, .png
- **Documents**: .pdf, .docx
- **Max Size**: 10MB

## Dependencies

- express: Web framework
- multer: File upload handling
- axios: HTTP client for LLM API
- tesseract.js: OCR for images
- pdf-parse: PDF text extraction
- mammoth: DOCX text extraction
- dotenv: Environment variables
- cors: Cross-origin resource sharing