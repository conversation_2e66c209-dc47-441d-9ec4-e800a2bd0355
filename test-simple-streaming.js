const axios = require('axios');

async function testSimpleStreaming() {
  try {
    console.log('🚀 Testing simple streaming with math problem...');
    
    const response = await axios.post('http://localhost:3001/api/chat/stream', {
      query: 'Solve this step by step: What is the integral of 2x + 3?'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      responseType: 'stream',
      timeout: 60000
    });
    
    console.log('✅ Streaming connection established');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    let startTime = Date.now();
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log('\n\n✅ Simple streaming completed!');
            console.log('📊 Statistics:');
            console.log(`   - Total content: ${fullContent.length} characters`);
            console.log(`   - Duration: ${duration} seconds`);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received:', {
                query: parsed.query.substring(0, 50) + '...',
                hasFile: parsed.hasFile,
                streaming: parsed.streaming
              });
              console.log('\n🤖 AI Response (streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing simple streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

console.log('🧪 Testing simple streaming functionality...');
console.log('🔗 Backend URL: http://localhost:3001/api/chat/stream');
console.log('');

testSimpleStreaming();
