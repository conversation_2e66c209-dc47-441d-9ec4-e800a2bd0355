const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function extractTextFromImage() {
  try {
    // Source image path
    const sourceImagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    // Check if source image exists
    if (!fs.existsSync(sourceImagePath)) {
      console.log('❌ Source image not found at:', sourceImagePath);
      return;
    }
    
    console.log('✅ Image file found');
    console.log('📊 Image file size:', fs.statSync(sourceImagePath).size, 'bytes');
    
    // Create form data with a simple query to just extract and show the text
    const form = new FormData();
    form.append('file', fs.createReadStream(sourceImagePath));
    form.append('query', 'Please extract and show me all the text you can see in this image. Just list the text content without solving anything yet.');
    
    console.log('📤 Sending request to backend for text extraction...');
    
    // Make request to backend
    const response = await axios.post('http://localhost:3001/api/chat', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 120000, // 2 minutes timeout
    });
    
    console.log('✅ Response received from backend');
    
    if (response.data.success) {
      console.log('\n📝 EXTRACTED TEXT FROM IMAGE:');
      console.log('=' .repeat(80));
      console.log(response.data.extractedText);
      console.log('=' .repeat(80));
      
      console.log('\n🤖 AI RESPONSE:');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
    } else {
      console.log('❌ Backend returned unsuccessful response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error extracting text:');
    
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('📋 Response:', error.response.data);
    } else if (error.request) {
      console.error('🌐 Network error - no response received');
    } else {
      console.error('⚠️ Error:', error.message);
    }
  }
}

console.log('🚀 Starting text extraction test...');
extractTextFromImage();
