const axios = require('axios');

// Test that existing clients (without stream parameter) still work
async function testBackwardCompatibility() {
  try {
    console.log('🚀 Testing backward compatibility (no stream parameter)...');
    
    // This simulates an existing client that doesn't know about streaming
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 5 + 3?'
      // No stream parameter - should default to non-streaming
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });
    
    console.log('✅ Response received');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE (Backward Compatible):');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
      console.log('\n📊 Response metadata:');
      console.log('- Success:', response.data.success);
      console.log('- Response length:', response.data.response.length);
      console.log('- Query:', response.data.query);
      console.log('- Has extracted text:', !!response.data.extractedText);
      
      console.log('\n✅ Backward compatibility confirmed!');
      console.log('   - Existing clients work without modification');
      console.log('   - Response format unchanged');
      console.log('   - No streaming behavior when not requested');
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing backward compatibility:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test with explicit stream: false
async function testExplicitNonStreaming() {
  try {
    console.log('\n🚀 Testing explicit non-streaming (stream: false)...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'Calculate 10 * 7',
      stream: false // Explicitly disable streaming
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });
    
    console.log('✅ Response received');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE (Explicit Non-streaming):');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
      console.log('\n✅ Explicit non-streaming works!');
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing explicit non-streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test with Accept header for streaming
async function testAcceptHeaderStreaming() {
  try {
    console.log('\n🚀 Testing Accept header streaming...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2^3?'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream' // This should trigger streaming
      },
      responseType: 'stream',
      timeout: 60000
    });
    
    console.log('✅ Streaming connection established via Accept header');
    console.log('📡 Receiving streaming data...\n');
    
    let fullContent = '';
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            console.log('\n\n✅ Accept header streaming completed!');
            console.log('📊 Total content:', fullContent.length, 'characters');
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Metadata received via Accept header');
              console.log('\n🤖 AI Response (Accept header streaming):');
              console.log('-'.repeat(50));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '-'.repeat(50));
      console.log('🎯 Accept header stream ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing Accept header streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run all compatibility tests
async function runCompatibilityTests() {
  console.log('🧪 Testing backward compatibility and new streaming features...');
  console.log('🔗 Backend URL: http://localhost:3001/api/chat');
  console.log('📋 Ensuring existing clients continue to work unchanged');
  console.log('');
  
  await testBackwardCompatibility();
  
  // Wait between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testExplicitNonStreaming();
  
  // Wait between tests
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testAcceptHeaderStreaming();
  
  console.log('\n🎉 All compatibility tests completed!');
  console.log('✅ Existing functionality preserved');
  console.log('✅ New streaming features work');
  console.log('✅ Multiple ways to enable/disable streaming');
}

runCompatibilityTests();
