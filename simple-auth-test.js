const axios = require('axios');

async function testHealth() {
  try {
    console.log('🔍 Testing health endpoint...');
    const response = await axios.get('http://localhost:3001/health');
    console.log('✅ Health endpoint works');
    console.log('Status:', response.status);
    console.log('Firebase enabled:', response.data.authentication.firebase_enabled);
    console.log('Auth required:', response.data.authentication.auth_required);
    return true;
  } catch (error) {
    console.error('❌ Health endpoint failed:', error.message);
    return false;
  }
}

async function testWithoutAuth() {
  try {
    console.log('\n🔍 Testing API without authentication...');
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'Hello'
    });
    console.log('❌ Should have failed but succeeded');
    return false;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected - authentication required');
      console.log('Error:', error.response.data.error);
      console.log('Code:', error.response.data.code);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.message);
      return false;
    }
  }
}

async function testWithInvalidAuth() {
  try {
    console.log('\n🔍 Testing API with invalid token...');
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'Hello'
    }, {
      headers: {
        'Authorization': 'Bearer invalid-token-123'
      }
    });
    console.log('❌ Should have failed but succeeded');
    return false;
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejected - invalid token');
      console.log('Error:', error.response.data.error);
      console.log('Code:', error.response.data.code);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.message);
      return false;
    }
  }
}

async function runTests() {
  console.log('🧪 Simple Firebase Authentication Test');
  console.log('=====================================\n');
  
  const healthOk = await testHealth();
  if (!healthOk) {
    console.log('❌ Backend not responding, stopping tests');
    return;
  }
  
  const authTest1 = await testWithoutAuth();
  const authTest2 = await testWithInvalidAuth();
  
  console.log('\n📊 Test Results:');
  console.log('- Health endpoint:', healthOk ? '✅' : '❌');
  console.log('- Auth required:', authTest1 ? '✅' : '❌');
  console.log('- Invalid token rejected:', authTest2 ? '✅' : '❌');
  
  if (healthOk && authTest1 && authTest2) {
    console.log('\n🎉 All tests passed! Firebase authentication is working correctly.');
  } else {
    console.log('\n❌ Some tests failed. Check the backend logs.');
  }
}

runTests();
