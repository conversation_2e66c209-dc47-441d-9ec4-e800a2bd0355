const axios = require('axios');

async function testNonStreaming() {
  try {
    console.log('🚀 Testing non-streaming endpoint (existing functionality)...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is the derivative of x^2 + 3x + 5?'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });
    
    console.log('✅ Non-streaming response received');
    console.log('Status:', response.status);
    
    if (response.data.success) {
      console.log('\n🤖 AI RESPONSE (Non-streaming):');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
      console.log('\n📊 Response metadata:');
      console.log('- Query:', response.data.query);
      console.log('- Success:', response.data.success);
      console.log('- Response length:', response.data.response.length);
      console.log('- Has extracted text:', !!response.data.extractedText);
    } else {
      console.log('❌ Backend error:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing non-streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Response:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

console.log('🧪 Testing existing non-streaming functionality...');
console.log('🔗 Backend URL: http://localhost:3001/api/chat');
console.log('');

testNonStreaming();
