const fs = require('fs');
const path = require('path');

function setupFirebase() {
  const sourceFile = 'C:/Users/<USER>/Downloads/jubuddy-ai-firebase-adminsdk-fbsvc-1a9a57f962.json';
  const targetFile = path.join(__dirname, 'firebase-service-account.json');
  
  console.log('🔥 Setting up Firebase Admin SDK...');
  console.log('Source:', sourceFile);
  console.log('Target:', targetFile);
  
  try {
    // Check if source file exists
    if (!fs.existsSync(sourceFile)) {
      console.error('❌ Source Firebase service account file not found at:');
      console.error('   ', sourceFile);
      console.log('\n💡 Please ensure the file exists at the specified location.');
      console.log('   You can download it from Firebase Console → Project Settings → Service Accounts');
      return false;
    }
    
    // Copy the file
    fs.copyFileSync(sourceFile, targetFile);
    
    console.log('✅ Firebase service account file copied successfully!');
    console.log('📁 File location:', targetFile);
    
    // Verify the file is valid JSON
    try {
      const serviceAccount = JSON.parse(fs.readFileSync(targetFile, 'utf8'));
      console.log('✅ Service account file is valid JSON');
      console.log('📋 Project ID:', serviceAccount.project_id);
      console.log('📧 Client Email:', serviceAccount.client_email);
      
      return true;
    } catch (parseError) {
      console.error('❌ Service account file is not valid JSON:', parseError.message);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Failed to copy Firebase service account file:', error.message);
    
    if (error.code === 'ENOENT') {
      console.log('\n💡 Manual setup instructions:');
      console.log('1. Download the Firebase service account JSON file');
      console.log('2. Rename it to: firebase-service-account.json');
      console.log('3. Place it in the backend root directory');
    }
    
    return false;
  }
}

function checkFirebaseSetup() {
  const targetFile = path.join(__dirname, 'firebase-service-account.json');
  
  console.log('🔍 Checking Firebase setup...');
  
  if (fs.existsSync(targetFile)) {
    try {
      const serviceAccount = JSON.parse(fs.readFileSync(targetFile, 'utf8'));
      console.log('✅ Firebase service account file found and valid');
      console.log('📋 Project ID:', serviceAccount.project_id);
      console.log('📧 Client Email:', serviceAccount.client_email);
      console.log('🔐 Authentication will be ENABLED');
      return true;
    } catch (error) {
      console.error('❌ Firebase service account file exists but is invalid:', error.message);
      return false;
    }
  } else {
    console.log('⚠️ Firebase service account file not found');
    console.log('🔓 Authentication will be DISABLED');
    console.log('📁 Expected location:', targetFile);
    return false;
  }
}

// Run setup if called directly
if (require.main === module) {
  console.log('🚀 Firebase Setup Script');
  console.log('========================\n');
  
  const setupSuccess = setupFirebase();
  
  if (!setupSuccess) {
    console.log('\n🔄 Checking current setup...');
    checkFirebaseSetup();
  }
  
  console.log('\n📝 Next steps:');
  console.log('1. Start the backend: node index.js');
  console.log('2. Test authentication: node test-firebase-auth.js');
  console.log('3. Check health endpoint: curl http://localhost:3001/health');
}

module.exports = {
  setupFirebase,
  checkFirebaseSetup
};
