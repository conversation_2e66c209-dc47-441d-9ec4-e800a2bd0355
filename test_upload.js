const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testFileUpload() {
  try {
    console.log('🧪 Testing file upload functionality...');
    
    // Create form data
    const form = new FormData();
    form.append('query', 'Please solve the math problem in this file');
    form.append('file', fs.createReadStream('simple_test.txt'), {
      filename: 'simple_test.txt',
      contentType: 'text/plain'
    });

    // Make request
    const response = await axios.post('http://localhost:3002/api/chat', form, {
      headers: {
        ...form.getHeaders()
      },
      timeout: 30000
    });

    console.log('✅ SUCCESS! File upload worked!');
    console.log('📝 Query:', response.data.query);
    console.log('📄 Extracted Text:', response.data.extractedText);
    console.log('🤖 Response:', response.data.response);

  } catch (error) {
    console.error('❌ ERROR:', error.message);
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('📋 Response:', error.response.data);
    }
  }
}

// Test text-only first
async function testTextOnly() {
  try {
    console.log('🧪 Testing text-only query...');
    const response = await axios.post('http://localhost:3002/api/chat', {
      query: 'What is 5 + 3?'
    }, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000
    });

    console.log('✅ Text-only query works!');
    console.log('🤖 Response:', response.data.response.substring(0, 100) + '...');
  } catch (error) {
    console.error('❌ Text-only query failed:', error.message);
  }
}

async function runTests() {
  await testTextOnly();
  console.log('\n' + '='.repeat(50) + '\n');
  await testFileUpload();
}

runTests();
