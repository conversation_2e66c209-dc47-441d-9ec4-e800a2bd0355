const axios = require('axios');

// Test without authentication (should fail if Firebase is enabled)
async function testWithoutAuth() {
  try {
    console.log('🚀 Testing without authentication...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('✅ Request succeeded (Firebase auth disabled)');
    console.log('Status:', response.status);
    console.log('Response:', response.data.success ? 'Success' : 'Failed');
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('🔒 Authentication required (Firebase auth enabled)');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
      console.log('Code:', error.response.data.code);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test with invalid token (should fail)
async function testWithInvalidAuth() {
  try {
    console.log('\n🚀 Testing with invalid authentication token...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token-12345'
      },
      timeout: 30000
    });
    
    console.log('❌ Request should have failed but succeeded');
    console.log('Status:', response.status);
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Invalid token correctly rejected');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
      console.log('Code:', error.response.data.code);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test with malformed Authorization header
async function testWithMalformedAuth() {
  try {
    console.log('\n🚀 Testing with malformed authorization header...');
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'InvalidFormat token-12345'
      },
      timeout: 30000
    });
    
    console.log('❌ Request should have failed but succeeded');
    
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Malformed header correctly rejected');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
      console.log('Code:', error.response.data.code);
    } else {
      console.error('❌ Unexpected error:', error.message);
    }
  }
}

// Test health endpoint (should always work)
async function testHealthEndpoint() {
  try {
    console.log('\n🚀 Testing health endpoint (should be public)...');
    
    const response = await axios.get('http://localhost:3001/health', {
      timeout: 30000
    });
    
    console.log('✅ Health endpoint accessible');
    console.log('Status:', response.status);
    console.log('Firebase enabled:', response.data.authentication.firebase_enabled);
    console.log('Auth required:', response.data.authentication.auth_required);
    console.log('Message:', response.data.authentication.message);
    
  } catch (error) {
    console.error('❌ Health endpoint failed:', error.message);
  }
}

// Test with valid Firebase token (you need to provide a real token)
async function testWithValidAuth(token) {
  try {
    console.log('\n🚀 Testing with valid Firebase token...');
    
    if (!token) {
      console.log('⚠️ No valid token provided, skipping this test');
      console.log('   To test with a valid token, call: testWithValidAuth("your-firebase-id-token")');
      return;
    }
    
    const response = await axios.post('http://localhost:3001/api/chat', {
      query: 'What is 2 + 2?'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000
    });
    
    console.log('✅ Valid token accepted');
    console.log('Status:', response.status);
    console.log('Response:', response.data.success ? 'Success' : 'Failed');
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Valid token rejected');
      console.log('Status:', error.response.status);
      console.log('Error:', error.response.data.error);
    } else {
      console.error('❌ Network error:', error.message);
    }
  }
}

// Run all tests
async function runAuthTests() {
  console.log('🧪 Testing Firebase Authentication Integration...');
  console.log('🔗 Backend URL: http://localhost:3001');
  console.log('');
  
  await testHealthEndpoint();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testWithoutAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testWithInvalidAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testWithMalformedAuth();
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  await testWithValidAuth(); // No token provided
  
  console.log('\n🎉 Authentication tests completed!');
  console.log('\n📋 Summary:');
  console.log('   - Health endpoint should always be accessible');
  console.log('   - If Firebase is disabled: all endpoints work without auth');
  console.log('   - If Firebase is enabled: protected endpoints require valid tokens');
  console.log('   - Invalid/malformed tokens should be rejected');
  console.log('\n💡 To test with a real Firebase token:');
  console.log('   testWithValidAuth("your-firebase-id-token-here")');
}

// Export for manual testing
module.exports = {
  testWithoutAuth,
  testWithInvalidAuth,
  testWithMalformedAuth,
  testHealthEndpoint,
  testWithValidAuth,
  runAuthTests
};

// Run tests if called directly
if (require.main === module) {
  runAuthTests();
}
