const http = require('http');

// Test health endpoint
function testHealth() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(data);
          console.log('✅ Health check successful');
          console.log('Firebase enabled:', parsed.authentication.firebase_enabled);
          console.log('Auth required:', parsed.authentication.auth_required);
          resolve(true);
        } catch (error) {
          console.error('❌ Failed to parse health response:', error.message);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ Health check failed:', error.message);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.error('❌ Health check timeout');
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test API without auth
function testApiWithoutAuth() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({ query: 'Hello' });
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 401) {
          try {
            const parsed = JSON.parse(data);
            console.log('✅ Correctly rejected without auth');
            console.log('Error:', parsed.error);
            console.log('Code:', parsed.code);
            resolve(true);
          } catch (error) {
            console.error('❌ Failed to parse error response:', error.message);
            resolve(false);
          }
        } else {
          console.log('❌ Should have been rejected but got status:', res.statusCode);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.error('❌ API test failed:', error.message);
      resolve(false);
    });

    req.setTimeout(5000, () => {
      console.error('❌ API test timeout');
      req.destroy();
      resolve(false);
    });

    req.write(postData);
    req.end();
  });
}

async function runQuickTest() {
  console.log('🧪 Quick Firebase Authentication Test');
  console.log('====================================\n');
  
  console.log('🔍 Testing health endpoint...');
  const healthOk = await testHealth();
  
  if (healthOk) {
    console.log('\n🔍 Testing API without authentication...');
    const authTest = await testApiWithoutAuth();
    
    console.log('\n📊 Results:');
    console.log('- Health endpoint:', healthOk ? '✅' : '❌');
    console.log('- Auth required:', authTest ? '✅' : '❌');
    
    if (healthOk && authTest) {
      console.log('\n🎉 Firebase authentication is working correctly!');
    } else {
      console.log('\n❌ Some tests failed.');
    }
  } else {
    console.log('\n❌ Backend not responding.');
  }
}

runQuickTest();
