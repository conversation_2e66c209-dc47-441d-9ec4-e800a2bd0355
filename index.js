const express = require('express');
const multer = require('multer');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const { createWorker } = require('tesseract.js');
const pdf = require('pdf-parse');
const mammoth = require('mammoth');
const cors = require('cors');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    fieldSize: 1024 * 1024, // 1MB field size limit
    fields: 10, // Max 10 fields
    files: 1 // Max 1 file
  },
  fileFilter: (req, file, cb) => {
    console.log('🔍 File filter check:', {
      originalname: file.originalname,
      mimetype: file.mimetype,
      fieldname: file.fieldname
    });

    const allowedTypes = /jpeg|jpg|png|pdf|docx|txt/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = file.mimetype === 'image/jpeg' ||
                     file.mimetype === 'image/jpg' ||
                     file.mimetype === 'image/png' ||
                     file.mimetype === 'application/pdf' ||
                     file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                     file.mimetype === 'text/plain' ||
                     file.mimetype === 'application/octet-stream'; // Sometimes .txt files come as this

    if (mimetype && extname) {
      console.log('✅ File accepted');
      return cb(null, true);
    } else {
      console.log('❌ File rejected');
      cb(new Error('Only .jpg, .png, .pdf, and .docx files are allowed'));
    }
  }
});

// Health check endpoint with scientific capabilities info
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'JUBuddyAI Backend is running',
    capabilities: {
      scientific_fields: [
        'Mathematics (Basic to PhD level)',
        'Physics (Classical to Quantum)',
        'Chemistry (General to Advanced Organic/Inorganic)',
        'Statistics (Descriptive to Advanced Inference)',
        'Biology (Cell Biology to Systems Biology)',
        'Advanced Sciences (Computational, Theoretical, Applied)'
      ],
      features: [
        'Step-by-step problem solving',
        'Multi-level content detection',
        'Specialized scientific prompts',
        'Enhanced error handling',
        'Fallback model support',
        'OCR for scientific documents',
        'PDF/DOCX processing'
      ],
      models: {
        primary: process.env.AI_MODEL_NAME,
        fallback: 'meta-llama/llama-3.2-3b-instruct:free'
      }
    }
  });
});

// Scientific capabilities endpoint
app.get('/capabilities', (req, res) => {
  res.json({
    scientific_fields: {
      mathematics: {
        levels: ['Elementary', 'High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Arithmetic', 'Algebra', 'Geometry', 'Trigonometry', 'Calculus', 'Linear Algebra', 'Differential Equations', 'Complex Analysis', 'Real Analysis', 'Abstract Algebra', 'Topology', 'Number Theory', 'Mathematical Logic', 'Set Theory']
      },
      physics: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Mechanics', 'Thermodynamics', 'Electromagnetism', 'Optics', 'Modern Physics', 'Quantum Mechanics', 'Relativity', 'Statistical Mechanics', 'Condensed Matter', 'Particle Physics', 'Astrophysics', 'Nuclear Physics']
      },
      chemistry: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['General Chemistry', 'Organic Chemistry', 'Inorganic Chemistry', 'Physical Chemistry', 'Analytical Chemistry', 'Biochemistry', 'Materials Chemistry', 'Computational Chemistry', 'Medicinal Chemistry']
      },
      statistics: {
        levels: ['Basic', 'Intermediate', 'Advanced', 'PhD'],
        topics: ['Descriptive Statistics', 'Probability', 'Hypothesis Testing', 'Regression Analysis', 'ANOVA', 'Bayesian Statistics', 'Time Series', 'Machine Learning', 'Experimental Design', 'Biostatistics']
      },
      biology: {
        levels: ['High School', 'Undergraduate', 'Graduate', 'PhD'],
        topics: ['Cell Biology', 'Genetics', 'Evolution', 'Ecology', 'Molecular Biology', 'Biochemistry', 'Neuroscience', 'Developmental Biology', 'Systems Biology', 'Bioinformatics']
      }
    },
    processing_features: {
      content_detection: 'Automatic detection of scientific content type',
      specialized_prompts: 'Field-specific system prompts for optimal responses',
      step_by_step: 'Detailed step-by-step solutions for problems',
      error_handling: 'Enhanced error handling for complex scientific queries',
      fallback_models: 'Multiple AI models for reliability',
      file_processing: 'OCR and document processing for scientific papers'
    }
  });
});

// Extract text from image using Tesseract.js
async function extractTextFromImage(imagePath) {
  try {
    const worker = await createWorker();
    await worker.loadLanguage('eng');
    await worker.initialize('eng');
    
    const { data: { text } } = await worker.recognize(imagePath);
    await worker.terminate();
    
    return text.trim();
  } catch (error) {
    throw new Error(`OCR failed: ${error.message}`);
  }
}

// Extract text from PDF
async function extractTextFromPDF(pdfPath) {
  try {
    const dataBuffer = fs.readFileSync(pdfPath);
    const data = await pdf(dataBuffer);
    return data.text.trim();
  } catch (error) {
    throw new Error(`PDF parsing failed: ${error.message}`);
  }
}

// Extract text from DOCX
async function extractTextFromDOCX(docxPath) {
  try {
    const result = await mammoth.extractRawText({ path: docxPath });
    return result.value.trim();
  } catch (error) {
    throw new Error(`DOCX parsing failed: ${error.message}`);
  }
}

// Enhanced scientific content detection and processing
function detectContentType(text) {
  const content = text.toLowerCase();

  // Mathematical content detection
  const mathPatterns = [
    /[\d\+\-\*\/\=\(\)\^\√∫∑∏π∞≤≥≠±∆∇∂∈∉∪∩⊂⊃∀∃∴∵∠°]/,
    /\b(solve|calculate|equation|formula|derivative|integral|limit|matrix|algebra|geometry|trigonometry|calculus|statistics|probability|theorem|proof|logarithm|exponential|polynomial|quadratic|linear|differential|partial|vector|scalar|eigenvalue|eigenvector)\b/i
  ];

  // Physics content detection with enhanced optics support
  const physicsPatterns = [
    /\b(force|energy|momentum|velocity|acceleration|mass|gravity|friction|pressure|temperature|heat|thermodynamics|quantum|relativity|electromagnetic|wave|frequency|amplitude|photon|electron|proton|neutron|atom|nuclear|radioactive|mechanics|kinematics|dynamics|optics|electricity|magnetism|circuit|voltage|current|resistance|capacitance|inductance|field|potential|work|power|entropy|enthalpy|oscillation|pendulum|spring|collision|conservation|newton|einstein|planck|bohr|schrodinger|maxwell|faraday|coulomb|ohm|joule|watt|pascal|kelvin|celsius|fahrenheit)\b/i,
    // Enhanced optics and lens patterns
    /\b(lens|mirror|focal|length|convex|concave|refractive|index|refraction|reflection|image|object|distance|magnification|power|diopter|aberration|dispersion|interference|diffraction|polarization|birefringence|optical|ray|beam|prism|spectrum|wavelength|coherence)\b/i,
    // Complex multi-step problem indicators
    /\b(system|matrix|determine|calculate|find|solve|behind|front|placed|distance|height|size|position|nature)\b/i
  ];

  // Chemistry content detection
  const chemistryPatterns = [
    /\b(molecule|atom|element|compound|reaction|bond|ionic|covalent|metallic|oxidation|reduction|acid|base|ph|buffer|catalyst|enzyme|equilibrium|kinetics|thermochemistry|electrochemistry|organic|inorganic|polymer|isomer|stereochemistry|spectroscopy|chromatography|titration|molarity|molality|stoichiometry|periodic|table|electron|configuration|orbital|hybridization|resonance|lewis|structure|vsepr|intermolecular|van der waals|hydrogen bonding|solubility|precipitation|crystallization|distillation|extraction|synthesis|mechanism|nucleophile|electrophile|substitution|elimination|addition|aromatic|aliphatic|functional group|carbohydrate|protein|lipid|nucleic acid|amino acid|peptide|enzyme|metabolism|photosynthesis|respiration)\b/i
  ];

  // Statistics content detection
  const statisticsPatterns = [
    /\b(mean|median|mode|variance|deviation|correlation|regression|hypothesis|test|significance|confidence|interval|distribution|normal|binomial|poisson|chi.?square|t.?test|anova|p.?value|null|alternative|sample|population|random|probability|bayes|likelihood|estimation|inference|descriptive|inferential|parametric|nonparametric|outlier|quartile|percentile|histogram|boxplot|scatterplot|contingency|table|survey|experiment|observational|study|bias|confounding|randomization|stratification|cluster|sampling|bootstrap|monte carlo|markov|chain|time series|forecasting|machine learning|data mining|big data|analytics)\b/i
  ];

  // Biology content detection
  const biologyPatterns = [
    /\b(cell|dna|rna|protein|gene|chromosome|mitosis|meiosis|evolution|natural selection|mutation|adaptation|species|ecosystem|biodiversity|photosynthesis|respiration|metabolism|enzyme|hormone|neuron|brain|nervous system|immune system|circulatory|respiratory|digestive|excretory|reproductive|endocrine|muscular|skeletal|integumentary|homeostasis|genetics|heredity|allele|genotype|phenotype|dominant|recessive|mendel|darwin|taxonomy|classification|kingdom|phylum|class|order|family|genus|bacteria|virus|fungi|plant|animal|vertebrate|invertebrate|mammal|bird|reptile|amphibian|fish|arthropod|mollusk|cnidarian|ecology|biome|habitat|niche|food chain|food web|producer|consumer|decomposer|symbiosis|parasitism|mutualism|commensalism|population|community|succession|conservation|endangered|extinction|biodiversity|biotechnology|genetic engineering|cloning|stem cell|cancer|disease|pathogen|antibiotic|vaccine|immunity)\b/i
  ];

  // Advanced scientific fields detection
  const advancedPatterns = [
    /\b(quantum mechanics|relativity|thermodynamics|statistical mechanics|solid state|condensed matter|particle physics|nuclear physics|astrophysics|cosmology|string theory|field theory|group theory|topology|differential geometry|complex analysis|functional analysis|measure theory|stochastic|processes|partial differential|equations|numerical analysis|computational|fluid dynamics|materials science|nanotechnology|biochemistry|molecular biology|biophysics|neuroscience|cognitive science|artificial intelligence|machine learning|deep learning|neural networks|algorithms|data structures|complexity theory|cryptography|information theory|signal processing|control theory|optimization|operations research|game theory|decision theory|econometrics|mathematical finance|actuarial science|epidemiology|biostatistics|clinical trials|meta analysis|systematic review)\b/i
  ];

  const isMath = mathPatterns.some(pattern => pattern.test(content));
  const isPhysics = physicsPatterns.some(pattern => pattern.test(content));
  const isChemistry = chemistryPatterns.some(pattern => pattern.test(content));
  const isStatistics = statisticsPatterns.some(pattern => pattern.test(content));
  const isBiology = biologyPatterns.some(pattern => pattern.test(content));
  const isAdvanced = advancedPatterns.some(pattern => pattern.test(content));

  // Detect complex multi-step problems
  const isComplexProblem = (text.match(/\d+\./g) || []).length >= 2 || // Multiple numbered items
                          /\b(step|steps|part|parts|section|sections)\b/i.test(content) ||
                          /\b(determine|calculate|find|solve)\b/i.test(content) && text.length > 200;

  // Detect specific optics problems
  const isOptics = /\b(lens|mirror|focal|convex|concave|refractive|refraction|reflection|image|object|distance|magnification|power|diopter)\b/i.test(content);

  // Detect if this might cause timeout (very complex content)
  const isHighComplexity = isAdvanced || (isComplexProblem && text.length > 500) ||
                          (isOptics && isComplexProblem) ||
                          text.length > 1000;

  return {
    isMath,
    isPhysics,
    isChemistry,
    isStatistics,
    isBiology,
    isAdvanced,
    isComplexProblem,
    isOptics,
    isHighComplexity,
    isScientific: isMath || isPhysics || isChemistry || isStatistics || isBiology || isAdvanced || isOptics
  };
}

// Generate specialized system prompts based on content type
function generateSystemPrompt(contentType) {
  if (!contentType.isScientific) {
    return 'You are a helpful AI assistant. Provide clear, accurate, and helpful responses to user queries.';
  }

  let prompt = 'You are an expert AI assistant specializing in scientific and academic content. ';

  if (contentType.isMath) {
    prompt += 'For mathematical problems: (1) Provide step-by-step solutions, (2) Show all work clearly, (3) Explain each step, (4) Use proper mathematical notation, (5) Verify your answers when possible. ';
  }

  if (contentType.isPhysics) {
    prompt += 'For physics problems: (1) Identify given information and what to find, (2) State relevant principles/laws, (3) Set up equations systematically, (4) Solve step-by-step with units, (5) Check if the answer makes physical sense, (6) Explain the underlying physics concepts. ';

    if (contentType.isOptics) {
      prompt += 'For optics problems specifically: (1) Draw ray diagrams when helpful, (2) Use lens/mirror equations correctly, (3) Apply sign conventions consistently, (4) Calculate focal lengths, object/image distances, and magnifications step-by-step, (5) Determine image characteristics (real/virtual, upright/inverted, magnified/diminished). ';
    }

    if (contentType.isComplexProblem) {
      prompt += 'For multi-step problems: (1) Break down into smaller sub-problems, (2) Solve each part systematically, (3) Show intermediate results clearly, (4) Connect results between steps, (5) Provide a comprehensive final answer. ';
    }
  }

  if (contentType.isChemistry) {
    prompt += 'For chemistry problems: (1) Identify the type of chemical process, (2) Write balanced chemical equations, (3) Show stoichiometric calculations, (4) Include proper chemical nomenclature, (5) Explain reaction mechanisms when relevant, (6) Consider thermodynamics and kinetics. ';
  }

  if (contentType.isStatistics) {
    prompt += 'For statistics problems: (1) Identify the type of statistical test/analysis needed, (2) State assumptions clearly, (3) Show calculations step-by-step, (4) Interpret results in context, (5) Discuss limitations and potential sources of error, (6) Use proper statistical terminology. ';
  }

  if (contentType.isBiology) {
    prompt += 'For biology questions: (1) Use accurate scientific terminology, (2) Explain biological processes clearly, (3) Connect structure to function, (4) Discuss evolutionary context when relevant, (5) Include molecular/cellular details as appropriate, (6) Reference current scientific understanding. ';
  }

  if (contentType.isAdvanced) {
    prompt += 'For advanced topics: (1) Provide rigorous explanations appropriate for graduate/PhD level, (2) Include relevant mathematical formulations, (3) Discuss current research and open questions, (4) Reference key papers/researchers when appropriate, (5) Explain both theoretical foundations and practical applications. ';
  }

  prompt += 'Always maintain scientific accuracy, cite established principles, and indicate when something is beyond current scientific knowledge.';

  return prompt;
}

// Helper function to chunk complex problems for better processing
function chunkComplexProblem(text, contentType) {
  if (!contentType.isComplexProblem || text.length < 1000) {
    return [text]; // Return as single chunk if not complex or short enough
  }

  const chunks = [];
  const lines = text.split('\n');
  let currentChunk = '';
  let inProblemSection = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    // Detect problem boundaries (numbered items, sections, etc.)
    const isProblemStart = /^\s*\d+\.|^\s*(part|section|step)\s*\d+/i.test(line) ||
                          /^\s*[a-z]\)|^\s*\([a-z]\)/i.test(line);

    if (isProblemStart && currentChunk.length > 200) {
      // Start new chunk
      chunks.push(currentChunk.trim());
      currentChunk = line + '\n';
      inProblemSection = true;
    } else {
      currentChunk += line + '\n';
    }

    // If chunk gets too long, force a break
    if (currentChunk.length > 800) {
      chunks.push(currentChunk.trim());
      currentChunk = '';
      inProblemSection = false;
    }
  }

  // Add remaining content
  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim());
  }

  return chunks.length > 1 ? chunks : [text];
}

// Send query to OpenRouter API with enhanced scientific content handling
async function sendToLLM(query, extractedText = '', fileInfo = null, streaming = false, res = null) {
  console.log('sendToLLM called with:', {
    queryLength: query?.length || 0,
    hasExtractedText: !!extractedText,
    extractedTextLength: extractedText?.length || 0,
    fileInfo: fileInfo
  });

  const primaryModel = process.env.AI_MODEL_NAME;
  const fallbackModel = 'meta-llama/llama-3.2-3b-instruct:free';
  let contentType = { isScientific: false };
  let fullQuery = query;

  try {
    // Handle extracted text from files with better processing
    if (extractedText && extractedText.trim()) {
      const cleanExtractedText = extractedText.trim().replace(/\r\n/g, '\n').replace(/\r/g, '\n');

      if (cleanExtractedText.length === 0) {
        console.log('Warning: Extracted text is empty after cleaning');
        fullQuery = `${query}\n\n[Note: A file was uploaded but no readable text content was found.]`;
      } else {
        fullQuery = `User Query: ${query}\n\nExtracted Content:\n${cleanExtractedText}`;
        console.log(`Combined query length: ${fullQuery.length} characters`);

        if (fullQuery.length > 12000) {
          const maxExtractedLength = 12000 - query.length - 300;
          const truncatedText = cleanExtractedText.substring(0, maxExtractedLength) + '\n\n[Content truncated due to length...]';
          fullQuery = `User Query: ${query}\n\nExtracted Content (truncated):\n${truncatedText}`;
          console.log(`Query truncated to ${fullQuery.length} characters`);
        }
      }
    }

    // Detect content type for specialized handling
    contentType = detectContentType(fullQuery);

    // Special preprocessing for high-complexity problems
    if (contentType.isHighComplexity) {
      console.log('🧠 Detected high-complexity problem, applying specialized preprocessing...');

      if (contentType.isOptics && contentType.isComplexProblem) {
        fullQuery = `${fullQuery}\n\n[SYSTEM NOTE: This appears to be a complex optics problem with multiple parts. Please solve systematically, showing all steps clearly, and break down the solution into manageable sections.]`;
      } else if (contentType.isComplexProblem) {
        fullQuery = `${fullQuery}\n\n[SYSTEM NOTE: This appears to be a multi-step problem. Please solve each part systematically and show all intermediate steps clearly.]`;
      }
    }

    // Generate specialized system prompt
    const systemPrompt = generateSystemPrompt(contentType);

    // Configure request parameters based on content type
    let maxTokens = 4000;
    let temperature = 0.7;
    let timeout = 90000;

    if (contentType.isScientific) {
      maxTokens = 8000;
      temperature = 0.2;
      timeout = 120000;

      if (contentType.isAdvanced || contentType.isHighComplexity) {
        maxTokens = 10000;
        timeout = 180000;
      }

      if (contentType.isMath && contentType.isAdvanced) {
        temperature = 0.1;
      }

      if (contentType.isOptics && contentType.isComplexProblem) {
        maxTokens = 12000;
        timeout = 240000;
        temperature = 0.15;
      }
    }

    const requestConfig = {
      model: primaryModel,
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: fullQuery
        }
      ],
      max_tokens: maxTokens,
      temperature: temperature,
      stream: streaming
    };

    const axiosConfig = {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jubuddy-ai-backend.onrender.com',
        'X-Title': 'JUBuddyAI Backend - Scientific AI Assistant'
      },
      timeout: timeout,
      responseType: streaming ? 'stream' : 'json'
    };

    let response;
    let modelUsed = primaryModel;

    // Retry mechanism for complex problems
    const maxRetries = contentType.isHighComplexity ? 2 : 1;
    let retryCount = 0;

    while (retryCount <= maxRetries) {
      try {
        console.log(`Processing ${contentType.isScientific ? 'scientific' : 'general'} content with ${primaryModel} (attempt ${retryCount + 1}/${maxRetries + 1})`);
        console.log(`Query preview: ${fullQuery.substring(0, 200)}...`);

        if (retryCount > 0) {
          await new Promise(resolve => setTimeout(resolve, 2000 * retryCount));
        }

        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, requestConfig, axiosConfig);

        // Handle streaming response
        if (streaming && res) {
          return new Promise((resolve, reject) => {
            let fullContent = '';

            response.data.on('data', (chunk) => {
              const lines = chunk.toString().split('\n');

              for (const line of lines) {
                if (line.startsWith('data: ')) {
                  const data = line.slice(6);

                  if (data === '[DONE]') {
                    res.write(`data: [DONE]\n\n`);
                    res.end();
                    resolve(fullContent);
                    return;
                  }

                  try {
                    const parsed = JSON.parse(data);
                    const delta = parsed.choices?.[0]?.delta;
                    let content = delta?.content || '';

                    // Handle DeepSeek R1 reasoning field in streaming
                    if (!content && delta?.reasoning) {
                      content = delta.reasoning;
                    }

                    if (content) {
                      fullContent += content;
                      res.write(`data: ${JSON.stringify({
                        content: content,
                        fullContent: fullContent,
                        done: false
                      })}\n\n`);
                    }
                  } catch (parseError) {
                    // Skip invalid JSON lines
                  }
                }
              }
            });

            response.data.on('end', () => {
              if (!res.headersSent) {
                res.write(`data: [DONE]\n\n`);
                res.end();
              }
              resolve(fullContent);
            });

            response.data.on('error', (error) => {
              console.error('Streaming error:', error);
              if (!res.headersSent) {
                res.write(`data: ${JSON.stringify({error: error.message})}\n\n`);
                res.end();
              }
              reject(error);
            });
          });
        }

        break;
      } catch (retryError) {
        retryCount++;
        if (retryCount > maxRetries) {
          throw retryError;
        }
        console.log(`Attempt ${retryCount} failed, retrying... Error: ${retryError.message}`);
      }
    }

    // If primary model failed completely, try fallback approaches
    if (!response) {
      console.log('Primary model failed, trying fallback approaches...');

      // For complex problems, try chunking approach first
      if (contentType.isHighComplexity) {
        try {
          console.log('🔄 Attempting chunked processing for complex problem...');
          const chunks = chunkComplexProblem(fullQuery, contentType);

          if (chunks.length > 1) {
            console.log(`Breaking problem into ${chunks.length} chunks`);
            let combinedResponse = '';

            for (let i = 0; i < chunks.length && i < 3; i++) {
              const chunkConfig = {
                model: primaryModel,
                messages: [
                  {
                    role: 'system',
                    content: `${systemPrompt} Focus on this specific part of a larger problem. Provide a complete solution for this section.`
                  },
                  {
                    role: 'user',
                    content: `Part ${i + 1} of ${chunks.length}:\n\n${chunks[i]}`
                  }
                ],
                max_tokens: 4000,
                temperature: temperature
              };

              const chunkResponse = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, chunkConfig, axiosConfig);
              if (chunkResponse.data?.choices?.[0]?.message?.content) {
                combinedResponse += `\n\n**Part ${i + 1} Solution:**\n${chunkResponse.data.choices[0].message.content}`;
              }
            }

            if (combinedResponse.trim()) {
              console.log('✅ Successfully processed complex problem using chunking approach');
              return `**Complex Problem Solution (Processed in Parts):**${combinedResponse}`;
            }
          }
        } catch (chunkError) {
          console.log('Chunking approach failed, trying fallback model...');
        }
      }

      // Try fallback model with simplified request
      try {
        const fallbackConfig = {
          model: fallbackModel,
          messages: [
            {
              role: 'system',
              content: contentType.isScientific
                ? 'You are a helpful AI assistant specializing in scientific content. Provide clear, step-by-step solutions to scientific problems.'
                : 'You are a helpful AI assistant. Provide clear, accurate responses to user queries.'
            },
            {
              role: 'user',
              content: fullQuery.length > 8000 ? fullQuery.substring(0, 8000) + '\n\n[Content truncated for fallback processing]' : fullQuery
            }
          ],
          max_tokens: contentType.isScientific ? 6000 : 3000,
          temperature: contentType.isScientific ? 0.3 : 0.7
        };

        modelUsed = fallbackModel;
        console.log(`Retrying with fallback model: ${fallbackModel}`);
        response = await axios.post(`${process.env.OPENROUTER_BASE_URL}/chat/completions`, fallbackConfig, axiosConfig);
      } catch (fallbackError) {
        console.error('Fallback model also failed:', fallbackError.message);
        throw new Error('All models failed to process the request');
      }
    }

    // Validate response
    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Invalid response structure from LLM API');
    }

    // Extract content from response - DeepSeek R1 may put content in reasoning field
    const messageObj = response.data.choices[0].message;
    let content = messageObj.content;

    // If content is empty but reasoning exists (DeepSeek R1 behavior), use reasoning
    if ((!content || content.trim() === '') && messageObj.reasoning) {
      console.log('🔍 Content field empty, using reasoning field from DeepSeek R1');
      content = messageObj.reasoning;
    }

    console.log('🔍 Final extracted content length:', content?.length || 0);

    if (!content || content.trim() === '') {
      console.error('Empty response received from AI model:', {
        model: modelUsed,
        hasExtractedText: !!extractedText,
        queryLength: fullQuery?.length || 0,
        contentType: contentType,
        responseData: response.data
      });

      let errorMessage = 'Empty response from AI model';
      if (contentType.isHighComplexity) {
        errorMessage += ' - Complex problems may require breaking down into smaller parts. Try asking about one specific aspect of the problem at a time.';
      } else if (contentType.isScientific) {
        errorMessage += ' - This may be due to content filtering for scientific content. Try rephrasing your question or providing more context.';
      } else {
        errorMessage += ' - This may be due to content filtering, rate limits, or model issues. Please try rephrasing your question.';
      }

      throw new Error(errorMessage);
    }

    // Log successful processing
    console.log(`Successfully processed ${contentType.isScientific ? 'scientific' : 'general'} content using ${modelUsed}`);

    const responseMetadata = {
      contentType: contentType,
      modelUsed: modelUsed,
      tokensUsed: response.data.usage?.total_tokens || 'unknown',
      processingTime: Date.now()
    };

    console.log('Response metadata:', responseMetadata);
    return content;

  } catch (error) {
    console.error('LLM API Error Details:', {
      message: error.message,
      response: error.response?.data,
      status: error.response?.status,
      timeout: error.code === 'ECONNABORTED',
      contentType: contentType,
      isScientific: contentType?.isScientific || false
    });

    if (error.code === 'ECONNABORTED') {
      const timeoutMsg = contentType?.isScientific
        ? 'Request timeout: Complex scientific problems can take time to process. Please try breaking your question into smaller parts or simplifying the problem.'
        : 'Request timeout: The AI is taking too long to process your content. Please try with a simpler question.';
      throw new Error(timeoutMsg);
    } else if (error.response) {
      const errorMsg = error.response.data?.error?.message || error.response.statusText || 'Unknown API error';
      const scientificNote = contentType?.isScientific
        ? ' Note: This appears to be scientific content. If the error persists, try rephrasing your question or breaking it into smaller parts.'
        : '';
      throw new Error(`LLM API error: ${errorMsg}${scientificNote}`);
    } else if (error.request) {
      throw new Error('LLM API request failed: No response received from the server. Please check your internet connection and try again.');
    } else {
      throw new Error(`Unexpected error: ${error.message}`);
    }
  }
}

// Main chat endpoint (legacy)
app.post('/chat', upload.single('file'), async (req, res) => {
  try {
    const { query } = req.body;

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    let extractedText = '';

    if (req.file) {
      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          console.log('Processing image with OCR...');
          extractedText = await extractTextFromImage(filePath);
        } else if (fileExtension === '.pdf') {
          console.log('Processing PDF document...');
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          console.log('Processing DOCX document...');
          extractedText = await extractTextFromDOCX(filePath);
        } else if (fileExtension === '.txt') {
          console.log('Processing TXT document...');
          extractedText = fs.readFileSync(filePath, 'utf8').trim();
          console.log(`Extracted ${extractedText.length} characters from TXT file`);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    if (req.file && !extractedText) {
      return res.status(400).json({
        error: 'No text could be extracted from the provided file'
      });
    }

    const llmResponse = await sendToLLM(query.trim(), extractedText);

    // Validate response
    if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
      throw new Error('Empty or invalid response from AI model');
    }

    res.json({
      success: true,
      query: query.trim(),
      extractedText: extractedText || null,
      response: llmResponse.trim()
    });

  } catch (error) {
    console.error('Error in /chat endpoint:', {
      message: error.message,
      stack: error.stack,
      hasFile: !!req.file,
      fileName: req.file?.originalname,
      fileSize: req.file?.size
    });

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage
    });
  }
});

// API versioned chat endpoint with streaming support
app.post('/api/chat', upload.single('file'), async (req, res) => {
  try {
    const { query, stream } = req.body;
    const isStreaming = stream === true || stream === 'true' || req.headers['accept'] === 'text/event-stream';

    if (!query || typeof query !== 'string' || query.trim() === '') {
      return res.status(400).json({
        error: 'Query is required and must be a non-empty string'
      });
    }

    // Set headers for streaming if requested
    if (isStreaming) {
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });
    }

    let extractedText = '';

    if (req.file) {
      const logMessage = isStreaming ? 'for streaming' : '';
      console.log(`🔍 Processing uploaded file ${logMessage}:`, {
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype,
        path: req.file.path
      });

      const filePath = req.file.path;
      const fileExtension = path.extname(req.file.originalname).toLowerCase();
      console.log('📄 File extension detected:', fileExtension);

      try {
        if (fileExtension === '.jpg' || fileExtension === '.jpeg' || fileExtension === '.png') {
          console.log('Processing image with OCR...');
          extractedText = await extractTextFromImage(filePath);
        } else if (fileExtension === '.pdf') {
          console.log('Processing PDF document...');
          extractedText = await extractTextFromPDF(filePath);
        } else if (fileExtension === '.docx') {
          console.log('Processing DOCX document...');
          extractedText = await extractTextFromDOCX(filePath);
        } else if (fileExtension === '.txt') {
          console.log('Processing TXT document...');
          extractedText = fs.readFileSync(filePath, 'utf8').trim();
          console.log(`Extracted ${extractedText.length} characters from TXT file`);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);
      } catch (error) {
        // Clean up file on error
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath);
        }
        throw error;
      }
    }

    if (req.file && !extractedText) {
      if (isStreaming) {
        res.write(`data: ${JSON.stringify({
          error: 'No text could be extracted from the provided file'
        })}\n\n`);
        res.end();
        return;
      } else {
        return res.status(400).json({
          error: 'No text could be extracted from the provided file'
        });
      }
    }

    if (isStreaming) {
      // Send initial metadata for streaming
      res.write(`data: ${JSON.stringify({
        type: 'metadata',
        query: query.trim(),
        extractedText: extractedText || null,
        hasFile: !!req.file,
        streaming: true
      })}\n\n`);

      // Call sendToLLM with streaming enabled
      const fullResponse = await sendToLLM(query.trim(), extractedText, null, true, res);
      console.log('✅ Streaming response completed');
    } else {
      // Non-streaming response (existing functionality)
      const llmResponse = await sendToLLM(query.trim(), extractedText);

      // Validate response
      if (!llmResponse || typeof llmResponse !== 'string' || llmResponse.trim() === '') {
        throw new Error('Empty or invalid response from AI model');
      }

      res.json({
        success: true,
        query: query.trim(),
        extractedText: extractedText || null,
        response: llmResponse.trim()
      });
    }

  } catch (error) {
    console.error('Error in /api/chat endpoint:', error);

    // Clean up file on error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // Provide more specific error messages
    let errorMessage = 'An internal server error occurred';
    let statusCode = 500;

    if (error.message.includes('timeout')) {
      errorMessage = 'Request timeout: Please try again with a simpler question';
      statusCode = 408;
    } else if (error.message.includes('LLM API error')) {
      errorMessage = error.message;
      statusCode = 502;
    } else if (error.message.includes('Empty or invalid response')) {
      errorMessage = 'The AI model returned an empty response. Please try rephrasing your question.';
      statusCode = 502;
    } else if (error.message) {
      errorMessage = error.message;
    }

    if (isStreaming) {
      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({
          error: errorMessage
        })}\n\n`);
        res.end();
      }
    } else {
      res.status(statusCode).json({
        success: false,
        error: errorMessage
      });
    }
  }
});



// Error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Error middleware triggered:', {
    name: error.name,
    message: error.message,
    code: error.code,
    stack: error.stack?.split('\n')[0]
  });

  if (error instanceof multer.MulterError) {
    console.error('📁 Multer error details:', error);
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large. Maximum size is 10MB' });
    }
    if (error.code === 'LIMIT_FIELD_COUNT') {
      return res.status(400).json({ error: 'Too many fields in form' });
    }
    if (error.code === 'LIMIT_FIELD_SIZE') {
      return res.status(400).json({ error: 'Field value too large' });
    }
    return res.status(400).json({ error: `Upload error: ${error.message}` });
  }

  if (error.message.includes('Unexpected end of form')) {
    return res.status(400).json({ error: 'Invalid multipart form data. Please check your file upload format.' });
  }

  if (error.message.includes('Only .jpg, .png, .pdf, .docx, and .txt files are allowed')) {
    return res.status(400).json({ error: error.message });
  }

  console.error('❌ Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
console.log('Starting JUBuddyAI Backend...');
app.listen(port, () => {
  console.log(`✅ JUBuddyAI Backend running on port ${port}`);
  console.log(`🔍 Health check: http://localhost:${port}/health`);
  console.log(`🚀 API endpoint: http://localhost:${port}/api/chat`);
});