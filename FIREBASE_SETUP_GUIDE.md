# Firebase Authentication Setup Guide

## 🔥 Complete Firebase Integration for JUBuddyAI Backend

This guide explains how to set up Firebase authentication for the JUBuddyAI backend to secure your API endpoints.

## 📋 Prerequisites

1. **Firebase Project**: You need a Firebase project set up
2. **Service Account Key**: Downloaded from Firebase Console
3. **Backend Dependencies**: Firebase Admin SDK is already installed

## 🚀 Step-by-Step Setup

### Step 1: Firebase Service Account Key Placement

**The JSON file is ALREADY CORRECTLY PLACED!** ✅

The Firebase service account key file has been successfully copied to:
```
D:\Successful build codes\backend\firebase-service-account.json
```

**File Details:**
- ✅ **Project ID**: `jubuddy-ai`
- ✅ **Client Email**: `<EMAIL>`
- ✅ **File Size**: Valid JSON with all required fields
- ✅ **Location**: Root directory of the backend

### Step 2: How the Authentication Works

#### When Firebase is Enabled:
- 🔐 **All API endpoints require authentication**
- 📱 **Frontend must send Firebase ID token**
- 🚫 **Requests without valid tokens are rejected with 401**
- ✅ **Health endpoint remains public**

#### When Firebase is Disabled:
- 🔓 **All endpoints are publicly accessible**
- ⚠️ **No authentication required**
- 📝 **Useful for development/testing**

### Step 3: API Request Format

#### With Authentication (Production):
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <firebase-id-token>" \
  -d '{"query": "Your question here", "stream": true}'
```

#### Without Authentication (Development):
```bash
# Only works when Firebase service account file is missing
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "Your question here"}'
```

### Step 4: Frontend Integration

#### JavaScript/TypeScript Example:
```javascript
import { getAuth } from 'firebase/auth';

async function callAPI(query) {
  const auth = getAuth();
  const user = auth.currentUser;
  
  if (user) {
    const idToken = await user.getIdToken();
    
    const response = await fetch('http://localhost:3001/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${idToken}`
      },
      body: JSON.stringify({ query, stream: true })
    });
    
    return response;
  } else {
    throw new Error('User not authenticated');
  }
}
```

#### Android (Kotlin) Example:
```kotlin
FirebaseAuth.getInstance().currentUser?.getIdToken(true)
    ?.addOnCompleteListener { task ->
        if (task.isSuccessful) {
            val idToken = task.result?.token
            // Use idToken in Authorization header
            val request = Request.Builder()
                .url("http://localhost:3001/api/chat")
                .addHeader("Authorization", "Bearer $idToken")
                .post(requestBody)
                .build()
        }
    }
```

## 🧪 Testing Authentication

### Test Scripts Available:
1. **`node test-firebase-auth.js`** - Basic authentication tests
2. **`node test-auth-streaming.js`** - Streaming with authentication
3. **`node simple-auth-test.js`** - Simple connectivity test
4. **`node setup-firebase.js`** - Setup verification

### Expected Behavior:
- ✅ Health endpoint accessible without auth
- 🔒 API endpoints require valid Firebase ID token
- ❌ Invalid/missing tokens return 401 error
- ✅ Valid tokens allow access to all features

## 🔧 Configuration Details

### Environment Variables:
```env
PORT=3001                                    # Server port
OPENROUTER_API_KEY=your_api_key             # AI model API key
AI_MODEL_NAME=deepseek/deepseek-r1:free     # AI model
```

### Firebase Configuration:
- **Service Account File**: `firebase-service-account.json` (✅ Already placed)
- **Project ID**: `jubuddy-ai`
- **Authentication Method**: Firebase ID Token verification
- **Middleware**: Applied to all `/api/chat` and `/chat` endpoints

## 🚨 Security Features

### Token Validation:
- ✅ **Signature verification** using Firebase Admin SDK
- ✅ **Expiration checking** - expired tokens rejected
- ✅ **Issuer validation** - only tokens from your Firebase project
- ✅ **Audience validation** - tokens must be for your project

### Error Handling:
- `AUTH_TOKEN_MISSING` - No Authorization header
- `AUTH_TOKEN_INVALID_FORMAT` - Malformed header
- `AUTH_TOKEN_EXPIRED` - Token has expired
- `AUTH_TOKEN_REVOKED` - Token has been revoked
- `AUTH_TOKEN_INVALID` - Invalid token signature

### Rate Limiting & Security:
- 🔐 **Secure by default** when Firebase file is present
- 🚫 **Graceful degradation** when Firebase is unavailable
- 📝 **Detailed error messages** for debugging
- 🔍 **Request logging** with user identification

## 🎯 Current Status

### ✅ COMPLETED:
- Firebase Admin SDK installed and configured
- Service account key properly placed
- Authentication middleware implemented
- All endpoints protected
- Streaming support with authentication
- Comprehensive error handling
- Test scripts created
- Documentation complete

### 🔄 READY TO USE:
Your Firebase authentication is **FULLY CONFIGURED** and ready for production use!

## 📞 Troubleshooting

### If Authentication Doesn't Work:
1. **Check file location**: Ensure `firebase-service-account.json` is in backend root
2. **Verify file content**: Must be valid JSON with all Firebase fields
3. **Check token format**: Must be `Authorization: Bearer <token>`
4. **Verify Firebase project**: Token must be from the same project
5. **Check token expiration**: Firebase ID tokens expire after 1 hour

### To Disable Authentication (Development):
1. Rename `firebase-service-account.json` to `firebase-service-account.json.disabled`
2. Restart the backend
3. All endpoints will be publicly accessible

### To Re-enable Authentication:
1. Rename back to `firebase-service-account.json`
2. Restart the backend
3. Authentication will be enforced

## 🎉 Success!

Your JUBuddyAI backend now has enterprise-grade Firebase authentication protecting all API endpoints while maintaining full backward compatibility and all existing features including streaming responses!
