const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testImageQuestions() {
  try {
    // Source image path
    const sourceImagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    // Check if source image exists
    if (!fs.existsSync(sourceImagePath)) {
      console.log('❌ Source image not found at:', sourceImagePath);
      console.log('Please make sure the image file exists at the specified location.');
      return;
    }
    
    console.log('✅ Image file found at:', sourceImagePath);
    console.log('📊 Image file size:', fs.statSync(sourceImagePath).size, 'bytes');
    
    // Create form data
    const form = new FormData();
    form.append('file', fs.createReadStream(sourceImagePath));
    form.append('query', 'Please analyze this image and answer all the questions shown in it. Provide detailed step-by-step solutions for each question, showing all calculations and explaining the concepts involved. If there are multiple questions, please number your responses clearly.');
    
    console.log('📤 Sending request to backend...');
    console.log('🔍 Query: Analyze image and answer all questions with detailed solutions');
    
    // Make request to backend
    const response = await axios.post('http://localhost:3001/api/chat', form, {
      headers: {
        ...form.getHeaders(),
      },
      timeout: 300000, // 5 minutes timeout for complex processing
    });
    
    console.log('✅ Response received from backend');
    console.log('📋 Response status:', response.status);
    
    if (response.data.success) {
      console.log('\n🎯 BACKEND RESPONSE:');
      console.log('=' .repeat(80));
      
      if (response.data.extractedText) {
        console.log('\n📝 EXTRACTED TEXT FROM IMAGE:');
        console.log('-'.repeat(50));
        console.log(response.data.extractedText);
        console.log('-'.repeat(50));
      }
      
      console.log('\n🤖 AI ANALYSIS AND ANSWERS:');
      console.log('-'.repeat(50));
      console.log(response.data.response);
      console.log('-'.repeat(50));
      
      console.log('\n✅ Test completed successfully!');
    } else {
      console.log('❌ Backend returned unsuccessful response:', response.data);
    }
    
  } catch (error) {
    console.error('❌ Error testing image questions:');
    
    if (error.response) {
      console.error('📊 Status:', error.response.status);
      console.error('📋 Response:', error.response.data);
    } else if (error.request) {
      console.error('🌐 Network error - no response received');
      console.error('Make sure the backend is running on http://localhost:3001');
    } else {
      console.error('⚠️ Error:', error.message);
    }
  }
}

// Run the test
console.log('🚀 Starting image questions test...');
console.log('📸 Testing backend with image containing questions');
console.log('🔗 Backend URL: http://localhost:3001/api/chat');
console.log('');

testImageQuestions();
