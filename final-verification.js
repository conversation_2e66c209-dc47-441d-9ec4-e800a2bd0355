const fs = require('fs');
const path = require('path');

function verifyFirebaseSetup() {
  console.log('🔥 Firebase Authentication Setup Verification');
  console.log('='.repeat(50));
  
  // Check 1: Firebase Admin SDK dependency
  console.log('\n📦 Checking Firebase Admin SDK...');
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    if (packageJson.dependencies['firebase-admin']) {
      console.log('✅ Firebase Admin SDK installed:', packageJson.dependencies['firebase-admin']);
    } else {
      console.log('❌ Firebase Admin SDK not found in dependencies');
      return false;
    }
  } catch (error) {
    console.log('❌ Could not read package.json');
    return false;
  }
  
  // Check 2: Service account file
  console.log('\n🔑 Checking Firebase service account file...');
  const serviceAccountPath = path.join(__dirname, 'firebase-service-account.json');
  
  if (fs.existsSync(serviceAccountPath)) {
    console.log('✅ Service account file found at:', serviceAccountPath);
    
    try {
      const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
      
      // Verify required fields
      const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
      const missingFields = requiredFields.filter(field => !serviceAccount[field]);
      
      if (missingFields.length === 0) {
        console.log('✅ Service account file is valid');
        console.log('   📋 Project ID:', serviceAccount.project_id);
        console.log('   📧 Client Email:', serviceAccount.client_email);
        console.log('   🔐 Private Key:', serviceAccount.private_key ? 'Present' : 'Missing');
      } else {
        console.log('❌ Service account file missing fields:', missingFields.join(', '));
        return false;
      }
    } catch (error) {
      console.log('❌ Service account file is not valid JSON:', error.message);
      return false;
    }
  } else {
    console.log('❌ Service account file not found');
    console.log('   Expected location:', serviceAccountPath);
    return false;
  }
  
  // Check 3: Backend code integration
  console.log('\n🔧 Checking backend integration...');
  try {
    const indexJs = fs.readFileSync('index.js', 'utf8');
    
    const checks = [
      { name: 'Firebase Admin import', pattern: /require\(['"]firebase-admin['"]\)/ },
      { name: 'Firebase initialization', pattern: /admin\.initializeApp/ },
      { name: 'Authentication middleware', pattern: /verifyFirebaseToken/ },
      { name: 'Protected endpoints', pattern: /verifyFirebaseToken.*upload\.single/ }
    ];
    
    let allChecksPass = true;
    checks.forEach(check => {
      if (check.pattern.test(indexJs)) {
        console.log(`✅ ${check.name} - Found`);
      } else {
        console.log(`❌ ${check.name} - Missing`);
        allChecksPass = false;
      }
    });
    
    if (!allChecksPass) {
      return false;
    }
  } catch (error) {
    console.log('❌ Could not read index.js:', error.message);
    return false;
  }
  
  // Check 4: Test files
  console.log('\n🧪 Checking test files...');
  const testFiles = [
    'test-firebase-auth.js',
    'test-auth-streaming.js',
    'simple-auth-test.js',
    'setup-firebase.js'
  ];
  
  testFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - Available`);
    } else {
      console.log(`⚠️ ${file} - Missing (optional)`);
    }
  });
  
  // Check 5: Documentation
  console.log('\n📚 Checking documentation...');
  const docFiles = [
    'FIREBASE_SETUP_GUIDE.md',
    'README.md'
  ];
  
  docFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - Available`);
    } else {
      console.log(`❌ ${file} - Missing`);
    }
  });
  
  return true;
}

function showUsageInstructions() {
  console.log('\n🚀 Usage Instructions');
  console.log('='.repeat(30));
  
  console.log('\n1. Start the backend:');
  console.log('   node index.js');
  
  console.log('\n2. Test authentication:');
  console.log('   node test-firebase-auth.js');
  
  console.log('\n3. API Request with authentication:');
  console.log('   curl -X POST http://localhost:3001/api/chat \\');
  console.log('     -H "Content-Type: application/json" \\');
  console.log('     -H "Authorization: Bearer <your-firebase-id-token>" \\');
  console.log('     -d \'{"query": "Hello", "stream": true}\'');
  
  console.log('\n4. Health check (always public):');
  console.log('   curl http://localhost:3001/health');
  
  console.log('\n📖 For detailed instructions, see: FIREBASE_SETUP_GUIDE.md');
}

function main() {
  const setupValid = verifyFirebaseSetup();
  
  console.log('\n' + '='.repeat(50));
  if (setupValid) {
    console.log('🎉 FIREBASE AUTHENTICATION SETUP COMPLETE!');
    console.log('✅ All components are properly configured');
    console.log('🔐 Your API endpoints are now secured with Firebase');
    showUsageInstructions();
  } else {
    console.log('❌ SETUP INCOMPLETE');
    console.log('⚠️ Please fix the issues above before proceeding');
  }
  console.log('='.repeat(50));
}

main();
