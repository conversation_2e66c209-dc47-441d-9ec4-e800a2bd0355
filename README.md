# JUBuddyAI Backend

Node.js + Express backend for JUBuddyAI1 Android app with OCR and LLM integration.

## Features

- **Text Input**: Direct LLM interaction
- **Image Processing**: OCR extraction from JPG/PNG using Tesseract.js
- **PDF Processing**: Text extraction using pdf-parse
- **DOCX Processing**: Text extraction using mammoth
- **LLM Integration**: OpenRouter API with DeepSeek R1
- **Streaming Support**: Real-time response streaming
- **Firebase Authentication**: Secure API access with Firebase ID tokens

## Installation

```bash
npm install
```

## Environment Variables

Create a `.env` file with:

```env
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
AI_MODEL_NAME=deepseek/deepseek-r1:free
PORT=3001
```

## Firebase Authentication Setup (Optional)

1. **Download Service Account Key**:
   - Go to Firebase Console → Project Settings → Service Accounts
   - Click "Generate new private key" and download the JSON file
   - Rename it to `firebase-service-account.json`
   - Place it in the backend root directory

2. **Authentication Behavior**:
   - **With Firebase**: All API endpoints require `Authorization: Bearer <firebase-id-token>` header
   - **Without Firebase**: All endpoints are publicly accessible (authentication disabled)
   - Health endpoint (`/health`) is always public

3. **Testing Authentication**:
   ```bash
   node test-firebase-auth.js
   ```

## API Endpoints

### Health Check
```http
GET /health
```

### Chat Endpoint
```http
POST /api/chat
Content-Type: multipart/form-data
Authorization: Bearer <firebase-id-token>

Body:
- query (required): string - User's question
- file (optional): file - Image (.jpg/.png), PDF (.pdf), or Word (.docx)
- stream (optional): boolean - Enable streaming responses (true/false)
```

**Note**: Authorization header is required only if Firebase authentication is enabled.

## Usage Examples

### 1. Text Only Request
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-firebase-id-token>" \
  -d '{"query": "What is artificial intelligence?"}'
```

### 2. Image Upload with OCR
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Authorization: Bearer <your-firebase-id-token>" \
  -F "query=What text is in this image?" \
  -F "file=@/path/to/image.jpg"
```

### 3. Streaming Response
```bash
curl -X POST http://localhost:3001/api/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-firebase-id-token>" \
  -d '{"query": "Explain quantum mechanics", "stream": true}'
```

### 3. PDF Upload
```bash
curl -X POST http://localhost:3001/chat \
  -F "query=Summarize this document" \
  -F "file=@/path/to/document.pdf"
```

### 4. DOCX Upload
```bash
curl -X POST http://localhost:3001/chat \
  -F "query=What are the key points?" \
  -F "file=@/path/to/document.docx"
```

## Response Format

```json
{
  "success": true,
  "query": "User's question",
  "extractedText": "Text extracted from file (if provided)",
  "response": "LLM response"
}
```

## Error Handling

The API provides comprehensive error handling for:
- Invalid file types
- File size limits (10MB max)
- OCR failures
- LLM API errors
- Missing required fields

## Deployment

### Render Deployment

1. Create a new Web Service on Render
2. Connect your GitHub repository
3. Set environment variables in Render dashboard
4. Build command: `npm install`
5. Start command: `node index.js`

### Environment Variables for Render

Add these to your Render environment variables:
- `OPENROUTER_API_KEY`: Your OpenRouter API key
- `PORT`: 10000 (Render's default)

## File Structure

```
├── index.js          # Main server file
├── package.json      # Dependencies and scripts
├── .env             # Environment variables
├── uploads/         # Temporary file uploads (auto-created)
└── README.md        # This file
```

## Supported File Types

- **Images**: .jpg, .jpeg, .png
- **Documents**: .pdf, .docx
- **Max Size**: 10MB

## Dependencies

- express: Web framework
- multer: File upload handling
- axios: HTTP client for LLM API
- tesseract.js: OCR for images
- pdf-parse: PDF text extraction
- mammoth: DOCX text extraction
- dotenv: Environment variables
- cors: Cross-origin resource sharing