const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

async function testPhysicsImageStreaming() {
  try {
    console.log('🚀 Testing physics image streaming...');
    
    const imagePath = 'C:/Users/<USER>/Downloads/photo_2025-07-21_14-45-46.jpg';
    
    if (!fs.existsSync(imagePath)) {
      console.log('❌ Physics image not found at:', imagePath);
      return;
    }
    
    console.log('✅ Physics image found');
    console.log('📊 Image size:', fs.statSync(imagePath).size, 'bytes');
    
    const form = new FormData();
    form.append('file', fs.createReadStream(imagePath));
    form.append('query', 'Please solve all the physics problems in this image step by step. Show detailed calculations and explain the concepts involved.');
    
    console.log('📤 Starting streaming request...');
    
    const response = await axios.post('http://localhost:3001/api/chat/stream', form, {
      headers: {
        ...form.getHeaders(),
      },
      responseType: 'stream',
      timeout: 300000 // 5 minutes for complex physics problems
    });
    
    console.log('✅ Streaming connection established');
    console.log('📡 Receiving physics solutions...\n');
    
    let fullContent = '';
    let startTime = Date.now();
    
    response.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          
          if (data === '[DONE]') {
            const duration = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log('\n\n✅ Physics streaming completed!');
            console.log('📊 Statistics:');
            console.log(`   - Total content: ${fullContent.length} characters`);
            console.log(`   - Duration: ${duration} seconds`);
            console.log(`   - Average speed: ${(fullContent.length / duration).toFixed(0)} chars/sec`);
            return;
          }
          
          try {
            const parsed = JSON.parse(data);
            
            if (parsed.type === 'metadata') {
              console.log('📋 Request metadata:');
              console.log(`   - Query: ${parsed.query.substring(0, 80)}...`);
              console.log(`   - Has file: ${parsed.hasFile}`);
              console.log(`   - Extracted text: ${parsed.extractedTextLength} characters`);
              console.log(`   - Streaming: ${parsed.streaming}`);
              console.log('\n🤖 AI Physics Solutions (streaming):');
              console.log('=' .repeat(80));
            } else if (parsed.content) {
              process.stdout.write(parsed.content);
              fullContent += parsed.content;
            } else if (parsed.error) {
              console.error('\n❌ Streaming error:', parsed.error);
            }
          } catch (parseError) {
            // Skip invalid JSON lines
          }
        }
      }
    });
    
    response.data.on('end', () => {
      console.log('\n' + '=' .repeat(80));
      console.log('🎯 Physics streaming ended successfully');
    });
    
    response.data.on('error', (error) => {
      console.error('❌ Stream error:', error.message);
    });
    
  } catch (error) {
    console.error('❌ Error testing physics streaming:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('Network error - no response received');
    } else {
      console.error('Error:', error.message);
    }
  }
}

console.log('🧪 Testing physics image streaming functionality...');
console.log('🔗 Backend URL: http://localhost:3001/api/chat/stream');
console.log('📸 Image: Physics problems with optics and lens calculations');
console.log('');

testPhysicsImageStreaming();
