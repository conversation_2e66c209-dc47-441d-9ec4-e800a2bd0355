<!DOCTYPE html>
<html>
<head>
    <title>Test Image Generator</title>
</head>
<body>
    <canvas id="canvas" width="400" height="200" style="border: 1px solid black;"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Fill background
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, 400, 200);
        
        // Add text
        ctx.fillStyle = 'black';
        ctx.font = '24px Arial';
        ctx.fillText('Hello World!', 50, 50);
        ctx.fillText('This is a test image', 50, 100);
        ctx.fillText('for OCR testing.', 50, 150);
        
        // Convert to blob and download
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test_image.png';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    </script>
</body>
</html>
